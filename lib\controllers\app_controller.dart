import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../util/app_theme.dart';

final appCtrl = AppController();

class AppController with ChangeNotifier {
  final AppTheme _appTheme = AppTheme.fromType(ThemeType.light);
  AppTheme get appTheme => _appTheme;
  bool isTheme = false;
  bool isRTL = true;
  String languageVal = "ar";

  String get currencySymbol => languageVal == 'ar' ? currencySymbolAR : currencySymbolEN;
  String currencySymbolAR = "د.ك";
  String currencySymbolEN = "KD";
  int currencyPrecision = 3;

  static String fcmToken = "";

  // static int shopType = 1;

  //************************************************************************* */

  // media query ********************
  static double W = AppController.mq.size.width;
  static double h = AppController.mq.size.height;
  // Global MediaQuery
  static MediaQueryData? _mediaQueryData;
  static MediaQueryData get mq => _mediaQueryData!;
  static void setMq(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
  }

  //************************************************************************* */
  static Future<bool> _checkFirstTime() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var isFirstTime = prefs.getBool('isFirstTime') ?? true;
    if (isFirstTime) {
      await prefs.setBool('isFirstTime', false);
    }

    return isFirstTime;
  }

  //************************************************************************* */
}
