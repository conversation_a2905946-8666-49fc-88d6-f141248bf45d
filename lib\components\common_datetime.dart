import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:shipping_system/util/config.dart';

class CommonDateTimePicker extends StatefulWidget {
  const CommonDateTimePicker(
      {Key? key,
      required this.initialVal,
      required this.onSave,
      required this.caption,
      required this.backColor,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final DateTime initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final double? fontSize;
  final String caption;
  @override
  // ignore: library_private_types_in_public_api
  _CommonDateTimePickerState createState() => _CommonDateTimePickerState();
}

class _CommonDateTimePickerState extends State<CommonDateTimePicker> {
  // DateTime selectedDate =widget.initialVal;
  TextEditingController _dateController = TextEditingController();

  @override
  void didChangeDependencies() {
    _dateController = TextEditingController(
        text: DateFormat('yyyy-MM-dd hh:mm', 'en').format(widget.initialVal));
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _selectDate(context);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: widget.backColor,
        ),
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        child: Text(
          tr(widget.caption),
          style: TextStyle(fontSize: widget.fontSize ?? 14),
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime minDate = DateTime.now().add(const Duration(hours: 23));
    DateTime maxDate = DateTime.now().add(const Duration(days: 60));
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        minimumDate: minDate,
        maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            _dateController.text =
                DateFormat('yyyy-MM-dd hh:mm', 'en').format(value);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101),
      );
      if (picked != null) {
        setState(() {
          _dateController.text =
              DateFormat('yyyy-MM-dd hh:mm', 'en').format(picked);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}

//================//================//================//================//================
//================//================//================//================//================

class CommonDatePicker extends StatefulWidget {
  const CommonDatePicker(
      {Key? key,
      this.initialVal,
      required this.backColor,
      required this.onSave,
      required this.caption,
      this.fontSize,
      this.isDisabled,
      this.onChange})
      : super(key: key);
  final DateTime? initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String caption;
  final double? fontSize;
  final bool? isDisabled;
  @override
  // ignore: library_private_types_in_public_api
  _CommonDatePickerState createState() => _CommonDatePickerState();
}

class _CommonDatePickerState extends State<CommonDatePicker> {
  // DateTime selectedDate =widget.initialVal;
  TextEditingController _dateController = TextEditingController();

  @override
  void didChangeDependencies() {
    _dateController = widget.initialVal != null
        ? TextEditingController(
            text: DateFormat('yyyy-MM-dd', 'en')
                .format(widget.initialVal ?? DateTime.now()))
        : TextEditingController();
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.isDisabled == true) {
          return;
        }
        _selectDate(context);
      },
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: widget.backColor,
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        child: Text(
          _dateController.text,
          style: TextStyle(fontSize: widget.fontSize ?? 14),
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        // Provide a background color for the popup.
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.date,
        // minimumDate: minDate,
        // maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            _dateController.text = DateFormat('yyyy-MM-dd', 'en').format(value);
          });
          if (widget.onChange != null) widget.onChange!(value);
          print(value);
        },
      ));
    } else {
      // Android
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: appCtrl.appTheme.primary, // <-- SEE HERE
                onPrimary: appCtrl.appTheme.secondary, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  backgroundColor:
                      appCtrl.appTheme.primary, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          widget.onSave(picked);
          _dateController.text = DateFormat('yyyy-MM-dd', 'en').format(picked);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}

//================//================//================//================//================
//================//================//================//================//================
class CommonTimePicker extends StatefulWidget {
  const CommonTimePicker(
      {Key? key,
      required this.initialVal,
      required this.backColor,
      required this.onSave,
      this.isDisabled,
      required this.caption,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final TimeOfDay initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String caption;
  final double? fontSize;
  final bool? isDisabled;

  @override
  State<CommonTimePicker> createState() => _CommonTimePickerState();
}

class _CommonTimePickerState extends State<CommonTimePicker> {
  final TextEditingController _dateController = TextEditingController();

  @override
  void initState() {
    _dateController.text = widget.initialVal.toString().substring(10, 15);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.isDisabled == true) {
          return;
        }
        _selectTime(context);
      },
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: widget.backColor,
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 15),
        child: Text(
          _dateController.text,
          textAlign: TextAlign.center,
          softWrap: true,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(fontSize: widget.fontSize ?? 14),
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        // Provide a background color for the popup.
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectTime(BuildContext context) async {
    DateTime minDate = DateTime.now().add(const Duration(hours: 23));
    DateTime maxDate = DateTime.now().add(const Duration(days: 60));
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.time,
        use24hFormat: true,
        minimumDate: minDate,
        maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            TimeOfDay picked =
                TimeOfDay(hour: value.hour, minute: value.minute);
            _dateController.text = picked.format(context);
            widget.onSave(picked);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android

      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: widget.initialVal,
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: appCtrl.appTheme.secondary, // <-- SEE HERE
                onPrimary: appCtrl.appTheme.primary, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  backgroundColor:
                      appCtrl.appTheme.primary, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          widget.onSave(picked);
          _dateController.text = picked.format(context);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}
