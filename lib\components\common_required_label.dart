import 'package:flutter/material.dart';

class CommonRequiredLabel extends StatelessWidget {
  const CommonRequiredLabel(this.label, {super.key});
  final String label;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(),
          ),
          const Text(
            '*',
            style: TextStyle(color: Colors.red),
          ),
        ],
      ),
    );
  }
}

class CommonLabel extends StatelessWidget {
  const CommonLabel(this.label, {super.key});
  final String label;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(),
          ),
        ],
      ),
    );
  }
}
