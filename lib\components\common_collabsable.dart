import 'package:flutter/material.dart';

class CommonCollabsableContainer extends StatelessWidget {
  const CommonCollabsableContainer({
    Key? key,
    required this.label,
    this.isExpanded = true,
    required this.children,
  }) : super(key: key);

  final String label;
  final bool isExpanded;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0),
      ),
      child: ExpansionTile(
        childrenPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        title: Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.black),
        ),
        initiallyExpanded: isExpanded,
        children: children,
      ),
    );
  }
}
