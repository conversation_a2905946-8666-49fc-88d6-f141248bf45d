import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class PleaseWaitDialog extends StatelessWidget {
  const PleaseWaitDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          return Future.value(true);
        },
        child: AlertDial<PERSON>(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text('Please wait...'.tr()),
            ],
          ),
        ));
  }
}
