import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tesseract_ocr/ocr_engine_config.dart';
import 'package:tesseract_ocr/tesseract_ocr.dart';

import '../../util/config.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // String containerNumber = '';
  String containerType = '';
  List<dynamic> results = [];
  bool _isContainerConfirmed = false;
  final TextEditingController _containerNumberController =
      TextEditingController();
  BayPlanDetailModel data = BayPlanDetailModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: "Home Screen".tr(),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: <Widget>[
              CommonRequiredLabel('Container Number'.tr()),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: CommonTextField(
                      controller: _containerNumberController,
                      label: '',
                      onlyCapitalLetter: true,
                      hint: '',
                      onChanged: (p0) {
                        data.containerNumber = p0;
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.camera_alt),
                    onPressed: () {
                      pickImageFromCamera();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // if (!_isContainerConfirmed)
              CommonMaterialButton(
                label: 'Search'.tr(),
                future: searchContainer,
              ),
              if (_isContainerConfirmed)
                Column(
                  children: [
                    CommonRequiredLabel('POD'.tr()),
                    MyComboBox(
                      caption: '',
                      onSelect: (id, name) {
                        setState(() {
                          data.podId = id;
                        });
                      },
                      modalTitle: '',
                      selectedValue: data.podId,
                      data: LookupsController.ports,
                      width: double.infinity,
                    ),
                    const SizedBox(height: 10),
                    CommonRequiredLabel('Bay Number'.tr()),
                    CommonTextField(
                      initialValue: data.bayNumber ?? '',
                      label: '',
                      hint: '',
                      onChanged: (p0) {
                        data.bayNumber = p0;
                      },
                    ),
                    const SizedBox(height: 10),
                    CommonRequiredLabel('Loading Date'.tr()),
                    Row(
                      children: [
                        Expanded(
                          child: CommonDatePicker(
                            initialVal: data.loadingDate ?? DateTime.now(),
                            onSave: (date) {
                              data.loadingDate = date;
                            },
                            caption: data.loadingStringDate ?? '',
                            backColor: appCtrl.appTheme.boxBg,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: CommonTimePicker(
                            initialVal: TimeOfDay.fromDateTime(
                                data.loadingDate ?? DateTime.now()),
                            onSave: (time) {
                              data.loadingDate = DateTime(
                                data.loadingDate!.year,
                                data.loadingDate!.month,
                                data.loadingDate!.day,
                                time.hour,
                                time.minute,
                              );
                            },
                            caption: data.loadingStringTime ?? '',
                            backColor: appCtrl.appTheme.boxBg,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    CommonMaterialButton(
                      label: 'Save'.tr(),
                      future: onSave,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  // searchContainer
  Future<void> searchContainer() async {
    setState(() {
      _isContainerConfirmed = false;
    });
    var containerNumber = _containerNumberController.text;
    if (containerNumber.isEmpty) {
      errorSnackBar(message: 'Please enter container number'.tr());
      return;
    }
    RegExp regex = RegExp(r'^[A-Za-z]{4}\d{7}$');

    if (!regex.hasMatch(containerNumber)) {
      errorSnackBar(message: 'Invalid container number'.tr());
      return;
    }

    await context.read<LoadingController>().searchContainer(containerNumber);
    if (!mounted) return;

    var bayPlanDetailModel =
        context.read<LoadingController>().bayPlanDetailModel;
    if (bayPlanDetailModel == null) {
      errorSnackBar(message: 'Container not found'.tr());
      return;
    } else {
      setState(() {
        data = bayPlanDetailModel;
      });
    }

    setState(() {
      _isContainerConfirmed = true;
    });
  }

  // onSave
  Future<void> onSave() async {
    if (data.podId == null) {
      errorSnackBar(message: 'Please select POD'.tr());
      return;
    }
    if (data.bayNumber == null || data.bayNumber!.isEmpty) {
      errorSnackBar(message: 'Please enter bay number'.tr());
      return;
    }

    data.loadingDate ??= DateTime.now();

    var response = await Api.post(
        action: 'api/loading/SaveContainerDetails', body: data.toJson());
    if (response == null) {
      return;
    }
    if (response.isSuccess) {
      successSnackBar(message: 'Data saved successfully'.tr());
      setState(() {
        _isContainerConfirmed = false;
        _containerNumberController.clear();
        data = BayPlanDetailModel();
      });
    } else {
      errorSnackBar(message: 'Failed to save data'.tr());
    }
  }

  // pickImageFromCamera
  Future<void> pickImageFromCamera() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      showPleaseWait();
      // convert image to base64
      var file = await compressAndGetFile(image);
      if (file == null) return;

      final String base64Image =
          base64Encode(File(file.path).readAsBytesSync());
      // add data:image/jpeg;base64, to base64Image
      final String base64ImageWithHeader =
          'data:image/jpeg;base64,$base64Image';

      var bodyAsjson = jsonEncode({'content': base64ImageWithHeader});
      _performOcr(image.path);
      var request = await Api.postClassic(
          action: 'https://shipping-ocr.azurewebsites.net/api/ocr/upload',
          body: bodyAsjson);
      Navigator.of(context).pop();
      setState(() {
        results = request;
      });
      processContainerNumber(request);
      if (request != null) {
        print(request);
      }
    }
  }

  Future<void> _performOcr(String imagePath) async {
    try {
      // Default usage (uses OCREngine.defaultEngine, language 'eng')
      // String extractedText = await TesseractOcr.extractText(imagePath);

      // Example: Using Tesseract engine with a specific language
      final tesseractConfig = OCRConfig(
        language: 'eng', // Must match a .traineddata file in assets/tessdata
        engine: OCREngine.tesseract,
        // Optional Tesseract options:
        // options: {
        //   TesseractConfig.preserveInterwordSpaces: '1',
        //   TesseractConfig.pageSegMode: PageSegmentationMode.autoOsd,
        //   TesseractConfig.debugFile: '/path/to/debug.log', // Example option
        // },
      );
      String extractedTextTesseract = await TesseractOcr.extractText(
        imagePath,
        config: tesseractConfig,
      );
      print('Extracted Text (Tesseract): $extractedTextTesseract');

      // Example: Using Apple Vision engine (iOS only)
      final visionConfig = OCRConfig(
        engine: OCREngine.vision,
        language: 'eng', // Vision engine may also use language hint
      );
      // Check if running on iOS before using Vision
      if (Platform.isIOS) {
        String extractedTextVision = await TesseractOcr.extractText(
          imagePath,
          config: visionConfig,
        );
        print('Extracted Text (Vision): $extractedTextVision');
      }
    } catch (e) {
      print('Error performing OCR: $e');
    }
  }

  Future<XFile?> compressAndGetFile(XFile file) async {
    var fileExt = file.path.split('.').last;
    var newRandomfileName = '${DateTime.now().millisecondsSinceEpoch}.$fileExt';
    var targetPath =
        '${(await getTemporaryDirectory()).path}/$newRandomfileName';

    var result = await FlutterImageCompress.compressAndGetFile(
      file.path,
      targetPath,
      quality: 88,
    );

    return result;
  }

  // Process container number
  void processContainerNumber(List<dynamic> results) async {
    var firstPart = results[0];

    setState(() {
      // if firstpart length < 5 then
      if (firstPart.toString().length < 5) {
        _containerNumberController.text =
            results[0] + results[1].toString().replaceAll(" ", "");
        containerType = results[2];
      }

      // if firstpart length > 4
      if (firstPart.toString().length > 4) {
        _containerNumberController.text =
            results[0].toString().replaceAll(" ", "");
        containerType = results[1];
      }
    });
  }
}
