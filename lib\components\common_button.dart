import 'package:flutter/material.dart';

import '../controllers/app_controller.dart';

class CommonMaterialButton extends StatefulWidget {
  const CommonMaterialButton({
    super.key,
    required this.label,
    this.onPressed,
    this.width,
    this.height,
    this.future,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  });

  final String label;
  final void Function()? onPressed;
  final double? width;
  final double? height;
  final Future Function()? future;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;

  @override
  State<CommonMaterialButton> createState() => _CommonMaterialButtonState();
}

class _CommonMaterialButtonState extends State<CommonMaterialButton> {
  bool isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      minWidth: widget.width ?? MediaQuery.of(context).size.width,
      height: widget.height ?? 60,
      colorBrightness: appCtrl.isTheme ? Brightness.light : Brightness.dark,
      color: widget.backgroundColor ?? appCtrl.appTheme.primary,
      disabledColor: appCtrl.appTheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        // add border color
        side: BorderSide(
          color: widget.borderColor ?? appCtrl.appTheme.primary,
          width: 1,
        ),
      ),
      onPressed: isProcessing ? null : onPressed,
      child: isProcessing
          ? CircularProgressIndicator(
              color: appCtrl.appTheme.white,
            )
          : Text(widget.label,
              style: TextStyle(
                color: widget.textColor ?? appCtrl.appTheme.white,
              )),
    );
  }

  void onPressed() {
    isProcessing = true;
    setState(() {});
    if (widget.future != null) {
      widget.future!().then((value) {
        isProcessing = false;
        setState(() {});
      });
    } else {
      widget.onPressed!();
      isProcessing = false;
      setState(() {});
    }
  }
}
