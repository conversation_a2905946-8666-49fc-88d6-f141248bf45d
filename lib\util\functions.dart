import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../components/common_pleasewait.dart';
import '../main.dart';

void openScreen(BuildContext context, Widget screen) {
  Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => screen),
  );
}

//===================================================================================================
void openScreenReplace(BuildContext context, Widget screen) {
  Navigator.pushReplacement(
    context,
    MaterialPageRoute(builder: (context) => screen),
  );
}

//===================================================================================================
void openScreenPopUntilHome(BuildContext context, Widget screen) {
  Navigator.pushAndRemoveUntil(
    context,
    MaterialPageRoute(builder: (context) => screen),
    (Route<dynamic> route) => route.isFirst,
  );
}

//===================================================================================================
void openScreenPopAll(BuildContext context, Widget screen) {
  Navigator.pushAndRemoveUntil(
    context,
    MaterialPageRoute(builder: (context) => screen),
    (Route<dynamic> route) => false,
  );
}

//===================================================================================================
const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
String getRandomString(int length) => String.fromCharCodes(
      Iterable.generate(length, (_) => _chars.codeUnitAt(Random().nextInt(_chars.length))),
    );

//===================================================================================================
// show please wait dialog
void showPleaseWait({BuildContext? context}) {
  showDialog(
    context: context ?? navigatorKey.currentContext!,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return const PleaseWaitDialog();
    },
  );
}

//************************************************************************************************* */
String myDateFormatter(DateTime? date, {bool isShowDay = false, bool isShowTime = true, bool hideDateIfSameDay = false}) {
  var isEnglish = EasyLocalization.of(navigatorKey.currentContext!)!.locale.languageCode == 'en';
  if (date != null) {
    var str = '';
    str = DateFormat('yyyy/MM/dd', 'en').format(date);
    str += isShowDay ? DateFormat('  EEEE', isEnglish ? 'en' : 'ar').format(date) : '';
    if (isShowTime) str += DateFormat('  HH:mm', 'en').format(date);

    if (hideDateIfSameDay) {
      var now = DateTime.now();
      if (date.day + date.month != now.day + now.month) {
        str = DateFormat('yyyy/MM/dd', 'en').format(date);
      } else {
        str = isShowDay ? DateFormat('  EEEE', isEnglish ? 'en' : 'ar').format(date) : '';
        if (isShowTime) str += DateFormat('  hh:mm', 'en').format(date) + DateFormat(' a', isEnglish ? 'en' : 'ar').format(date);
      }
      // str = DateFormat('yyyy/MM/dd').format(date);
      // str += isShowDay ? DateFormat('  EEEE', 'ar').format(date) : '';
      // if (isShowTime) str += DateFormat('  hh:mm').format(date) + DateFormat(' a', 'ar').format(date);
    }

    return str;
  }
  return '';
}

Color getColorByStatus(int status) {
  switch (status) {
    case 0:
      return Colors.orange;
    case 1:
      return Colors.blue;
    case 2:
      return Colors.green;
    case 3:
      return Colors.green;
    case 4:
      return Colors.grey;
    case 5:
      return Colors.red;
    case 6:
      return Colors.green;

    default:
      return Colors.black;
  }
}
