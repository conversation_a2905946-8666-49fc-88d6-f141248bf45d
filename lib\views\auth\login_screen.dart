import 'package:easy_localization/easy_localization.dart' as es;
import '../../util/config.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  // final _formKey = GlobalKey<FormState>();
  bool obscureText = true;

  // String email = '';
  // String password = '';
  String email = 'tallyman';
  String password = '123456';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: MediaQuery.of(context).padding.top + 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: SizedBox(
                width: double.infinity,
                child: Center(
                  child: Text(
                    'Login'.tr(),
                    textAlign: TextAlign.center,
                    style: const TextStyle().copyWith(
                      letterSpacing: .01,
                      fontSize: 25,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: MediaQuery.of(context).size.height * .1),
            Center(
              child: Text('PAL4IT'.tr(),
                  style: const TextStyle().copyWith(
                    fontSize: 30,
                    color: appCtrl.appTheme.primary,
                  )),
            ),
            const SizedBox(height: 5),
            Center(
              child: Text(
                'SHIPPING SYSTEM'.tr(),
                style: const TextStyle().copyWith(
                  fontSize: 20,
                  color: appCtrl.appTheme.secondary.withOpacity(.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // email ********
                  CommonTextField(
                    initialValue: email,
                    label: 'Email'.tr(),
                    hint: 'Enter your email'.tr(),
                    keyboardType: TextInputType.emailAddress,
                    onChanged: (p0) => email = p0,
                  ),

                  const SizedBox(height: 20),
                  // password ********
                  CommonTextField(
                    initialValue: password,
                    label: 'Password'.tr(),
                    hint: 'Enter your password'.tr(),
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: obscureText,
                    onChanged: (p0) => password = p0,
                    suffixIcon: InkWell(
                      onTap: () {
                        setState(() {
                          obscureText = !obscureText;
                        });
                      },
                      child: obscureText ? const Icon(Icons.visibility) : const Icon(Icons.visibility_off),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // voyage list ********
                  MyComboBox(
                    caption: "Voyage".tr(),
                    data: LookupsController.voyages,
                    modalTitle: "Select Voyage".tr(),
                    selectedValue: context.watch<AuthController>().bayPlanId,
                    onSelect: (id, name) {
                      context.read<AuthController>().bayPlanId = id;
                    },
                    width: double.infinity,
                  ),

                  const SizedBox(height: 20),

                  CommonMaterialButton(
                    label: 'Login'.tr(),
                    future: onLogin,
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onLogin() async {
    if (email.isEmpty) {
      errorSnackBar(message: 'Please enter your email'.tr());
      return;
    }
    if (password.isEmpty) {
      errorSnackBar(message: 'Please enter your password'.tr());
      return;
    }
    if (context.read<AuthController>().bayPlanId == null) {
      errorSnackBar(message: 'Please select voyage'.tr());
      return;
    }

    var model = LoginModel(
      userName: email,
      password: password,
      bayPlanId: context.read<AuthController>().bayPlanId,
    );
    await Provider.of<AuthController>(context, listen: false).login(context, model);
  }
}
