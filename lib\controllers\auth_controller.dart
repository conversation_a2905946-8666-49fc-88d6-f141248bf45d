import 'package:easy_localization/easy_localization.dart';

import '../util/config.dart';
import '../views/home/<USER>';

class AuthController with ChangeNotifier {
  String userName = "";
  String password = "";
  //int? voyageId;
  int? bayPlanId;
  String? referenceNumber;

  bool get isAuth => userName != "";

  //============================================================================
  Future<ResponseResultModel> login(BuildContext context, LoginModel model) async {
    var result = await Api.post(action: 'api/auth/Login', body: model.toJson());
    if (result == null) {
      return ResponseResultModel(isSuccess: false);
    }

    try {
      if (result.isSuccess) {
        userName = model.userName!;
        password = model.password!;

        var pref = await SharedPreferences.getInstance();
        pref.setString('userName', userName);
        pref.setString('password', password);
        await LookupsController.getPorts();

        openScreenPopAll(navigatorKey.currentContext!, const HomeScreen());
        notifyListeners();
        //await init(context);
      } else {
        // show error
        errorSnackBar(message: tr(result.errors![0]));
      }
      return result;
    } catch (e) {
      return result;
    }
  }
}
