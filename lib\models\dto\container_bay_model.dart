class BayPlanDetailModel {
  int? id;
  String? containerNumber;
  int? podId;
  String? bayNumber;
  DateTime? loadingDate;
  String? get loadingStringDate => loadingDate?.toString().substring(0, 10);
  String? get loadingStringTime => loadingDate?.toString().substring(11, 16);

  BayPlanDetailModel({
    this.id,
    this.containerNumber,
    this.podId,
    this.bayNumber,
    this.loadingDate,
  });

  BayPlanDetailModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    containerNumber = json['containerNumber'];
    podId = json['podId'];
    bayNumber = json['bayNumber'];
    loadingDate = json['loadingDate'] != null ? DateTime.parse(json['loadingDate']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['containerNumber'] = containerNumber;
    data['podId'] = podId;
    data['bayNumber'] = bayNumber;
    data['loadingDate'] = loadingDate?.toIso8601String();
    return data;
  }
}
