import 'package:flutter/material.dart';

class CommonModernCheckbox extends StatelessWidget {
  const CommonModernCheckbox({
    super.key,
    required this.value,
    required this.title,
    this.subtitle,
    this.onChanged,
    required this.color,
  });

  final bool value;
  final String title;
  final String? subtitle;
  final Function(bool)? onChanged;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      value: value,
      title: Text(title),
      subtitle: Text(subtitle ?? ''),
      activeColor: color,
      trackOutlineColor: MaterialStateColor.resolveWith((states) => value ? color : Colors.grey),
      onChanged: (value) {
        onChanged?.call(value);
      },
    );
  }
}
