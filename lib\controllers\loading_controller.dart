import 'package:shipping_system/util/config.dart';

class LoadingController with ChangeNotifier {
  BayPlanDetailModel? bayPlanDetailModel;

  Future<void> searchContainer(String container) async {
    try {
      bayPlanDetailModel = null;

      var bayPlanId = navigatorKey.currentContext!.read<AuthController>().bayPlanId;
      var url = 'api/loading/GetContainerDetails?containerNumber=$container&bayPlanId=$bayPlanId';
      var response = await Api.getOne(action: url);
      if (response == null) {
        return;
      }
      if (response.statusCode == 200) {
        bayPlanDetailModel = BayPlanDetailModel.fromJson(response.data);
        notifyListeners();
      } else {
        return;
      }
    } catch (e) {
      Logger.logError(e);
      return;
    }
  }
}
