// ignore_for_file: avoid_print

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/response.dart';

// const String baseUrl = 'https://shipping-ocr.azurewebsites.net';
// const String baseUrl = 'https://test.tarabulus-sys.com';
const String baseUrl = 'http://************:444';
// const String baseUrl = 'https://shipping.pal4it.org';
// const String baseUrl = 'http://*************:45455';

class Api {
  static Dio api = Dio();

  static void initDio() {
    api.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          return handler.next(options);
        },
        onError: (error, handler) async {
          if ((error.response?.statusCode == 401)) {
            var pref = await SharedPreferences.getInstance();
            var refToken = pref.getString('refreshToken');
            if (refToken != null && refToken.isNotEmpty) {
              // if (await refreshToken()) {
              //   //return handler.resolve(await _retry(error.requestOptions));
              // }
            } else {
              //Get.offAllNamed(routeName.loginScreen);
            }
          }
          // else if (error.response?.statusCode == 403) {
          //   errorMsg(context: navigatorKey.currentContext!, msg: 'لا يوجد صلاحية للقيام بهذه العملية');
          //   print('ssssssssssssssssssssssssssss');
          // } else if (error.response?.statusCode == 500) {
          //   print(error.response?.data);
          // }

          return handler.next(error);
        },
      ),
    );
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> getOne({required String action}) async {
    try {
      var url = '$baseUrl/$action';
      var result = await api.get(url);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> post(
      {required String action, dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.post(url, data: body);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<dynamic> postClassic(
      {required String action, dynamic body}) async {
    var url = action;
    try {
      var result = await api.post(url, data: body);
      var data = result.data;
      return data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<dynamic> postList(
      {required String action, required dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.post(url, data: body);
      return result.data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> put(
      {required String action, dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.put(url, data: body);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While put Request, $url');
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> delete({required String action}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.delete(url);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While delete Request, $url');
      return null;
    }
  }

  //************************************************************************** */
  // static Future<Response<dynamic>> _retry(RequestOptions requestOptions) async {
  //   final options = Options(
  //     method: requestOptions.method,
  //     headers: requestOptions.headers,
  //   );
  //   return api.request<dynamic>(requestOptions.path, data: requestOptions.data, queryParameters: requestOptions.queryParameters, options: options);
  // }
}
