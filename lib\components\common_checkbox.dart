import 'package:flutter/material.dart';

import '../controllers/app_controller.dart';

class CommonCheckBox extends StatelessWidget {
  const CommonCheckBox({super.key, required this.value, this.onChanged, required this.label});
  final bool value;
  final String label;
  final Function(bool? val)? onChanged;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onChanged != null) onChanged!(!value);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: value,
            fillColor: MaterialStateProperty.all<Color>(appCtrl.appTheme.primary),
            onChanged: (p0) {
              if (onChanged != null) onChanged!(p0);
            },
          ),
          Text(label),
        ],
      ),
    );
  }
}
