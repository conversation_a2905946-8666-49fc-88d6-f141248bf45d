import '../util/config.dart';

class LookupsController {
  static List<ComboBoxDataModel> voyages = [];
  static List<ComboBoxDataModel> ports = [];

  //==============================================================
  static Future<void> getVoyages() async {
    try {
      var response = await Api.getOne(action: 'api/loading/BayPlanList');
      if (response != null && response.isSuccess) {
        voyages = [];
        for (var item in response.data) {
          voyages.add(ComboBoxDataModel.fromJson(item));
        }
      }
    } catch (e) {
      Logger.logError(e);
    }
  }

  //==============================================================
  static Future<void> getPorts() async {
    try {
      var bayPlanId = navigatorKey.currentContext!.read<AuthController>().bayPlanId;
      var response = await Api.getOne(action: 'api/loading/PODListByBayPlanId?bayPlanId=$bayPlanId');
      if (response != null && response.isSuccess) {
        ports = [];
        for (var item in response.data) {
          ports.add(ComboBoxDataModel.fromJson(item));
        }
      }
    } catch (e) {
      Logger.logError(e);
    }
  }
}
