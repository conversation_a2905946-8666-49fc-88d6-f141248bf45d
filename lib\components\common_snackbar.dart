import 'package:another_flushbar/flushbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../main.dart';

//===============================================
void errorSnackBar({required String message, String? title}) {
  Flushbar(
    title: title ?? 'Error!'.tr(),
    message: message,
    duration: const Duration(seconds: 3),
    animationDuration: const Duration(milliseconds: 200),
    forwardAnimationCurve: Curves.bounceIn,
    reverseAnimationCurve: Curves.bounceOut,
    flushbarPosition: FlushbarPosition.TOP,
    leftBarIndicatorColor: Colors.redAccent,
    icon: const Icon(
      Icons.info_outline,
      size: 28.0,
      color: Colors.redAccent,
    ),
    onTap: (flushbar) {
      flushbar.dismiss();
    },
  ).show(navigatorKey.currentContext!);
}

//===============================================================
void successSnackBar({required String message, String? title}) {
  Flushbar(
    title: title ?? 'Success!'.tr(),
    message: message,
    duration: const Duration(seconds: 3),
    animationDuration: const Duration(milliseconds: 200),
    forwardAnimationCurve: Curves.bounceIn,
    reverseAnimationCurve: Curves.bounceOut,
    flushbarPosition: FlushbarPosition.TOP,
    leftBarIndicatorColor: Colors.greenAccent,
    icon: const Icon(
      Icons.check_circle_outlined,
      size: 28.0,
      color: Colors.greenAccent,
    ),
    onTap: (flushbar) {
      flushbar.dismiss();
    },
  ).show(navigatorKey.currentContext!);
}

//===============================================================
Future<bool?> showConfirmDialog({
  String? title,
  String? content,
  String? backText,
  String? confirmText,
}) async {
  title ??= 'Confirm Operation'.tr();
  content ??= 'Are you sure you want to continue?'.tr();

  return showDialog<bool>(
    context: navigatorKey.currentContext!,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title!),
        content: SingleChildScrollView(
          child: ListBody(
            children: [
              Text(content!),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: Text(backText ?? 'Cancel'.tr()),
            onPressed: () {
              Navigator.of(context).pop(false);
            },
          ),
          TextButton(
            child: Text(confirmText ?? 'Confirm'.tr()),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
          ),
        ],
      );
    },
  );
}
