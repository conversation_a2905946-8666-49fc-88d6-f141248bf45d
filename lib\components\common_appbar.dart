import '../util/config.dart';

class MyAppBar extends StatefulWidget implements PreferredSizeWidget {
  const MyAppBar({super.key, required this.title, this.extraWidget});
  final String title;
  final Widget? extraWidget;
  @override
  State<MyAppBar> createState() => _MyAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _MyAppBarState extends State<MyAppBar> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(widget.title),
      // title: Row(
      //   mainAxisAlignment: widget.extraWidget != null ? MainAxisAlignment.start : MainAxisAlignment.center,
      //   children: [
      //     Text(widget.title),
      //     if (widget.extraWidget != null) const Spacer(),
      //     if (widget.extraWidget != null) widget.extraWidget!,
      //   ],
      // ),
      // centerTitle: true,
      actions: [widget.extraWidget ?? const SizedBox()],
      backgroundColor: appCtrl.appTheme.white,
      elevation: 0,
      iconTheme: IconThemeData(
        color: appCtrl.appTheme.primary,
        size: 30,
      ),
    );
  }
}
