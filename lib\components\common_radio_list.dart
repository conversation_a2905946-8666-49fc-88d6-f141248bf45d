import 'package:flutter/material.dart';

import '../controllers/app_controller.dart';
import 'common_combobox.dart';

class RadioList extends StatefulWidget {
  const RadioList({
    super.key,
    required this.data,
    this.selectedValue,
    // required this.title,
    required this.onSelect,
  });

  final List<ComboBoxDataModel> data;
  final int? selectedValue;
  // final String title;
  final Function(int id, String name) onSelect;

  @override
  State<RadioList> createState() => _RadioListState();
}

class _RadioListState extends State<RadioList> {
  @override
  Widget build(BuildContext context) {
    var data = widget.data;
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: data.length,
      itemBuilder: (context, index) {
        return RadioListTile(
          title: Text(data[index].name),
          activeColor: appCtrl.appTheme.primary,
          value: data[index].id,
          groupValue: widget.selectedValue,
          onChanged: (val) {
            widget.onSelect(val as int, data[index].name);
          },
        );
      },
    );
  }
}
