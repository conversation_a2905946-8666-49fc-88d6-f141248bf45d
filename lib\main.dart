import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:shipping_system/views/home/<USER>';
import './util/config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  EasyLocalization.logger.enableBuildModes = [];
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(EasyLocalization(
    path: 'assets/langs',
    supportedLocales: const [Locale('ar'), Locale('en')],
    saveLocale: true,
    useOnlyLangCode: true,
    child: MultiProvider(
      providers: [
        ChangeNotifierProvider<AuthController>(create: (ctx) => AuthController()),
        ChangeNotifierProvider<AppController>(create: (ctx) => AppController()),
        ChangeNotifierProvider<LoadingController>(create: (ctx) => LoadingController()),
      ],
      child: const MyApp(),
    ),
  ));
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    Api.initDio();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Shipping System',
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.light,
      navigatorKey: navigatorKey,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      theme: AppTheme.fromType(ThemeType.light).themeData,
      home: Builder(builder: (context) {
        AppController.setMq(context);
        return const SplashScreen();
      }),
    );
  }
}
