class ResponseResultModel {
  int statusCode = 0;
  bool isSuccess = false;
  List<dynamic>? errors;
  dynamic data;

  ResponseResultModel({
    this.data,
    this.errors,
    required this.isSuccess,
  });

  ResponseResultModel.fromJson(Map<dynamic, dynamic> json)
      : statusCode = json['statusCode'],
        isSuccess = json['isSuccess'],
        errors = json['errors'],
        data = json['data'];
}
