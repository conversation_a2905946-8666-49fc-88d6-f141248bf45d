import 'package:easy_localization/easy_localization.dart' as es;
import 'package:flutter/material.dart';

import '../controllers/app_controller.dart';

class ComboBoxDataModel {
  int id;
  String name;

  ComboBoxDataModel.fromJson(Map<dynamic, dynamic> json)
      : id = json["id"],
        name = json["name"];
  ComboBoxDataModel(this.id, this.name);
}

//****************************************************************************************************** */

class MyComboBox extends StatelessWidget {
  final String caption;
  final int? selectedValue;
  final String modalTitle;
  // final Future future;
  final List<ComboBoxDataModel> data;
  final Function(int id, String name) onSelect;
  final bool isSmallLookup;
  final double? width;
  final double? fontSize;
  final Color? backColor;
  final Color? fontColor;
  final double? height;
  final double? borderRadius;

  const MyComboBox({
    Key? key,
    required this.caption,
    // required this.future,
    required this.onSelect,
    required this.modalTitle,
    this.isSmallLookup = false,
    this.width,
    this.backColor,
    this.fontColor,
    this.fontSize = 14,
    this.borderRadius = 11,
    this.height,
    this.selectedValue,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? (caption.length <= 7 ? 8 : caption.length) * 12.4,
      height: height ?? 40, //AppController.mq.size.width / 2 - 50,
      // padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      child: InkWell(
        child: Card(
          margin: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(borderRadius ?? 10),
            ),
          ),
          color: backColor ?? appCtrl.appTheme.boxBg,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      selectedValue != null ? data.firstWhere((element) => element.id == selectedValue).name : caption,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      // textDirection: TextDirection.rtl,
                      style: TextStyle(fontSize: fontSize, color: fontColor ?? appCtrl.appTheme.secondary),
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: fontColor ?? appCtrl.appTheme.secondary,
                  size: isSmallLookup ? 18 : 20,
                ),
              ],
            ),
          ),
        ),
        onTap: () {
          showModalBottomSheet<void>(
            shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(25.0))),
            isScrollControlled: true,
            context: context,
            builder: (_) {
              return ComboBoxModal(
                // future: future,
                data: data,
                selectedValue: selectedValue,
                title: modalTitle,
                onSelect: onSelect,
              );
            },
          );
        },
      ),
    );
  }
}

//****************************************************************************************************** */

class ComboBoxModal extends StatefulWidget {
  const ComboBoxModal({
    Key? key,
    // required this.future,
    required this.title,
    required this.onSelect,
    this.selectedValue,
    required this.data,
  }) : super(key: key);
  // final Future future;
  final List<ComboBoxDataModel> data;
  final String title;
  final int? selectedValue;
  final Function(int id, String name) onSelect;
  @override
  // ignore: library_private_types_in_public_api
  _ComboBoxModalState createState() => _ComboBoxModalState();
}

class _ComboBoxModalState extends State<ComboBoxModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 8,
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      height: MediaQuery.of(context).viewInsets.bottom + (AppController.mq.size.height / 2.8),
      child: Column(
        children: [
          SizedBox(
            height: 30,
            child: Center(
              child: Text(
                widget.title,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, color: appCtrl.appTheme.primary),
              ),
            ),
          ),
          const Divider(thickness: 1),
          Expanded(
            child: ComboBoxListItems(
              data: widget.data,
              onSelect: widget.onSelect,
              selectedValue: widget.selectedValue,
            ),
          )
        ],
      ),
    );
  }
}

class ComboBoxListItems extends StatefulWidget {
  const ComboBoxListItems({super.key, required this.data, required this.onSelect, this.selectedValue});
  final List<ComboBoxDataModel> data;
  final Function(int id, String name) onSelect;
  final int? selectedValue;
  @override
  State<ComboBoxListItems> createState() => _ComboBoxListItemsState();
}

class _ComboBoxListItemsState extends State<ComboBoxListItems> {
  List<ComboBoxDataModel> myArr = [];
  ScrollController scrollController = ScrollController();
  @override
  void initState() {
    myArr = widget.data;
    super.initState();
    if (widget.selectedValue != null) {
      var selectedValue = myArr.firstWhere((element) => element.id == widget.selectedValue);
      var indexOfSelected = myArr.indexOf(selectedValue);
      print(indexOfSelected);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollController.animateTo(indexOfSelected * 40, duration: const Duration(milliseconds: 400), curve: Curves.easeIn);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 30,
          child: TextField(
            textAlign: TextAlign.center,
            decoration: InputDecoration(
              hintStyle: const TextStyle(fontSize: 14),
              hintText: es.tr('Search'),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(
                  color: appCtrl.appTheme.primary,
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
            ),
            onChanged: (val) {
              setState(() {
                myArr = widget.data.where((element) => element.name.trim().toLowerCase().contains(val.trim().toLowerCase())).toList();
              });
            },
          ),
        ),
        const SizedBox(height: 5),
        Expanded(
          child: ListView.builder(
            controller: scrollController,
            itemCount: myArr.length,
            itemBuilder: (context, index) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: InkWell(
                  splashColor: appCtrl.appTheme.primary.withOpacity(0.5),
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onSelect(myArr[index].id, myArr[index].name);
                  },
                  child: Card(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          myArr[index].name,
                          style: TextStyle(
                            color: myArr[index].id == widget.selectedValue ? appCtrl.appTheme.primary : appCtrl.appTheme.secondary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
