import 'package:flutter/material.dart';

import '../controllers/app_controller.dart';

class CommonTextField extends StatelessWidget {
  const CommonTextField({
    super.key,
    required this.label,
    required this.hint,
    this.obscureText,
    this.keyboardType,
    this.controller,
    this.validator,
    this.onSaved,
    this.onChanged,
    this.onTap,
    this.enabled,
    this.suffixIcon,
    this.initialValue,
    this.onlyCapitalLetter = false,
  });
  final String? initialValue;
  final String label;
  final String hint;
  final bool? obscureText;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Function(String?)? onSaved;
  final Function(String)? onChanged;
  final Function()? onTap;
  final bool? enabled;
  final Widget? suffixIcon;
  final bool onlyCapitalLetter;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: initialValue,
      keyboardType: keyboardType,
      autofocus: true,
      decoration: commonInputDecoration(label, hint, suffixIcon),
      obscureText: obscureText ?? false,
      controller: controller,
      validator: validator,
      textCapitalization: onlyCapitalLetter ? TextCapitalization.characters : TextCapitalization.none,
      onSaved: onSaved,
      onChanged: onChanged,
      onTap: onTap,
      enabled: enabled,
    );
  }
}

InputDecoration commonInputDecoration(String label, String hint, Widget? suffixIcon) {
  return InputDecoration(
    contentPadding: const EdgeInsets.all(10),
    border: const OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(10)),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: BorderSide(color: appCtrl.appTheme.border, width: 1),
    ),
    filled: true,
    fillColor: const Color(0xffF0F5FA),
    labelText: label,
    hintText: hint,
    suffixIcon: suffixIcon,
  );
}
