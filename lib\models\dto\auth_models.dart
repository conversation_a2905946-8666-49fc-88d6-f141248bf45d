class LoginModel {
  String? userName;
  String? password;
  int? bayPlanId;

  LoginModel({
    this.userName,
    this.password,
    this.bayPlanId,
  });

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    return LoginModel(
      userName: json['userName'],
      password: json['password'],
      bayPlanId: json['bayPlanId'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userName'] = userName;
    data['password'] = password;
    data['bayPlanId'] = bayPlanId;

    return data;
  }
}
