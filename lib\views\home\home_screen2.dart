import 'package:easy_localization/easy_localization.dart';
import '../../util/config.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String containerNumber = '';
  String containerType = '';
  List<dynamic> results = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text("Home Screen".tr()),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CommonMaterialButton(
              onPressed: () {
                pickImageFromCamera();
                // processContainerNumber([]);
              },
              label: 'Pick Image From Camera',
            ),
            const SizedBox(height: 20),
            Text('Container Number:', style: Theme.of(context).textTheme.labelLarge),
            Text(containerNumber, style: Theme.of(context).textTheme.labelLarge),
            const SizedBox(height: 20),
            Text('Container Type:', style: Theme.of(context).textTheme.labelLarge),
            Text(containerType, style: Theme.of(context).textTheme.labelLarge),
            const SizedBox(height: 10),
            const Divider(),
            Expanded(
              child: ListView.builder(
                itemCount: results.length,
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(results[index].toString()),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // pickImageFromCamera
  Future<void> pickImageFromCamera() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      showPleaseWait();
      // convert image to base64
      final String base64Image = base64Encode(File(image.path).readAsBytesSync());
      // add data:image/jpeg;base64, to base64Image
      final String base64ImageWithHeader = 'data:image/jpeg;base64,$base64Image';
      print(base64ImageWithHeader);
      var bodyAsjson = jsonEncode({'content': base64ImageWithHeader});
      var request = await Api.postClassic(action: 'api/ocr/upload', body: bodyAsjson);
      Navigator.of(context).pop();
      setState(() {
        results = request;
      });
      processContainerNumber(request);
      if (request != null) {
        print(request);
      }
    }
  }

  // Process container number
  void processContainerNumber(List<dynamic> results) async {
    var firstPart = results[0];

    setState(() {
      // if firstpart length < 5 then
      if (firstPart.toString().length < 5) {
        containerNumber = results[0] + results[1].toString().replaceAll(" ", "");
        containerType = results[2];
      }

      // if firstpart length > 4
      if (firstPart.toString().length > 4) {
        containerNumber = results[0].toString().replaceAll(" ", "");
        containerType = results[1];
      }
    });
  }
}
